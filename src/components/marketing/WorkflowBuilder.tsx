import { useState, useEffect } from 'react';
import { <PERSON><PERSON>, Dialog<PERSON>ontent, Di<PERSON><PERSON>eader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
  Plus, 
  Trash2, 
  ArrowRight, 
  MessageSquare, 
  Clock, 
  FileText,
  Save,
  X
} from 'lucide-react';
import { CreateWorkflowData, Workflow } from '@/hooks/useWorkflows';
import { StepConfigForm } from './StepConfigForm';
import { LEAD_STATUSES } from '@/constants/leadStatuses';

interface WorkflowBuilderProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (workflowData: CreateWorkflowData) => Promise<void>;
  editingWorkflow?: Workflow | null;
}

interface WorkflowStepData {
  step_type: 'send_whatsapp' | 'wait' | 'update_lead_status' | 'update_case_status' | 'create_case';
  step_config: any;
}

export const WorkflowBuilder = ({ 
  isOpen, 
  onClose, 
  onSave, 
  editingWorkflow 
}: WorkflowBuilderProps) => {
  const [currentStep, setCurrentStep] = useState(1);
  const [isSaving, setIsSaving] = useState(false);
  
  // Form data
  const [workflowName, setWorkflowName] = useState('');
  const [workflowDescription, setWorkflowDescription] = useState('');
  const [triggerType, setTriggerType] = useState<'lead_status_change' | 'case_status_change' | 'manual'>('lead_status_change');
  const [fromStatus, setFromStatus] = useState('any');
  const [toStatus, setToStatus] = useState('');
  const [targetStatuses, setTargetStatuses] = useState<string[]>([]);
  const [manualDescription, setManualDescription] = useState('');
  const [steps, setSteps] = useState<WorkflowStepData[]>([]);

  // Load editing workflow data
  useEffect(() => {
    if (editingWorkflow) {
      setWorkflowName(editingWorkflow.name);
      setWorkflowDescription(editingWorkflow.description || '');
      setTriggerType(editingWorkflow.trigger_type);

      if (editingWorkflow.trigger_type === 'manual') {
        setTargetStatuses(editingWorkflow.trigger_config.target_statuses || []);
        setManualDescription(editingWorkflow.trigger_config.description || '');
      } else {
        setFromStatus(editingWorkflow.trigger_config.from_status || 'any');
        setToStatus(editingWorkflow.trigger_config.to_status || '');
      }

      const workflowSteps = editingWorkflow.workflow_steps
        ?.sort((a, b) => a.step_order - b.step_order)
        .map(step => ({
          step_type: step.step_type,
          step_config: step.step_config
        })) || [];

      setSteps(workflowSteps);
    } else {
      // Reset form for new workflow
      setWorkflowName('');
      setWorkflowDescription('');
      setTriggerType('lead_status_change');
      setFromStatus('any');
      setToStatus('');
      setTargetStatuses([]);
      setManualDescription('');
      setSteps([]);
    }
    setCurrentStep(1);
  }, [editingWorkflow, isOpen]);

  const validateWorkflow = () => {
    const errors: string[] = [];

    // Basic validation
    if (!workflowName.trim()) {
      errors.push('שם הזרימה הוא שדה חובה');
    }

    // Trigger-specific validation
    if (triggerType === 'lead_status_change' || triggerType === 'case_status_change') {
      if (!toStatus) {
        errors.push('יש לבחור סטטוס יעד');
      }
    }

    if (triggerType === 'manual') {
      if (targetStatuses.length === 0) {
        errors.push('יש לבחור לפחות סטטוס אחד עבור טריגר ידני');
      }
    }

    if (steps.length === 0) {
      errors.push('יש להוסיף לפחות פעולה אחת');
    }

    // Validate each step
    steps.forEach((step, index) => {
      const stepNumber = index + 1;

      switch (step.step_type) {
        case 'send_whatsapp':
          if (!step.step_config.message?.trim()) {
            errors.push(`שלב ${stepNumber} (שלח וואטסאפ): הודעה היא שדה חובה`);
          }
          if (!step.step_config.recipient) {
            errors.push(`שלב ${stepNumber} (שלח וואטסאפ): יש לבחור נמען`);
          }
          break;

        case 'wait':
          if (!step.step_config.duration || step.step_config.duration < 1) {
            errors.push(`שלב ${stepNumber} (המתן): משך זמן הוא שדה חובה`);
          }
          if (!step.step_config.unit) {
            errors.push(`שלב ${stepNumber} (המתן): יחידת זמן היא שדה חובה`);
          }
          break;

        case 'update_lead_status':
          if (!step.step_config.status) {
            errors.push(`שלב ${stepNumber} (עדכן ליד): סטטוס חדש הוא שדה חובה`);
          }
          break;

        case 'update_case_status':
          if (!step.step_config.status) {
            errors.push(`שלב ${stepNumber} (עדכן תיק): סטטוס חדש הוא שדה חובה`);
          }
          break;

        case 'create_case':
          if (!step.step_config.title?.trim()) {
            errors.push(`שלב ${stepNumber} (צור תיק): כותרת התיק היא שדה חובה`);
          }
          if (!step.step_config.case_type_id) {
            errors.push(`שלב ${stepNumber} (צור תיק): סוג תיק הוא שדה חובה`);
          }
          break;
      }
    });

    return errors;
  };

  const handleSave = async () => {
    const validationErrors = validateWorkflow();

    if (validationErrors.length > 0) {
      alert('שגיאות בטופס:\n\n' + validationErrors.join('\n'));
      return;
    }

    setIsSaving(true);
    try {
      const workflowData: CreateWorkflowData = {
        name: workflowName.trim(),
        description: workflowDescription.trim() || undefined,
        trigger_type: triggerType,
        trigger_config: triggerType === 'manual' ? {
          target_statuses: targetStatuses,
          description: manualDescription.trim() || undefined
        } : {
          from_status: fromStatus,
          to_status: toStatus
        },
        steps: steps
      };

      await onSave(workflowData);
    } catch (error) {
      console.error('Error saving workflow:', error);
    } finally {
      setIsSaving(false);
    }
  };

  const addStep = (stepType: WorkflowStepData['step_type']) => {
    const newStep: WorkflowStepData = {
      step_type: stepType,
      step_config: {}
    };
    setSteps([...steps, newStep]);
  };

  const updateStep = (index: number, stepConfig: any) => {
    const updatedSteps = [...steps];
    updatedSteps[index].step_config = stepConfig;
    setSteps(updatedSteps);
  };

  const removeStep = (index: number) => {
    setSteps(steps.filter((_, i) => i !== index));
  };

  const moveStep = (index: number, direction: 'up' | 'down') => {
    if (
      (direction === 'up' && index === 0) ||
      (direction === 'down' && index === steps.length - 1)
    ) {
      return;
    }

    const newSteps = [...steps];
    const targetIndex = direction === 'up' ? index - 1 : index + 1;
    [newSteps[index], newSteps[targetIndex]] = [newSteps[targetIndex], newSteps[index]];
    setSteps(newSteps);
  };

  const getStepIcon = (stepType: string) => {
    switch (stepType) {
      case 'send_whatsapp':
        return <MessageSquare className="h-4 w-4" />;
      case 'wait':
        return <Clock className="h-4 w-4" />;
      case 'update_lead_status':
      case 'update_case_status':
        return <ArrowRight className="h-4 w-4" />;
      case 'create_case':
        return <FileText className="h-4 w-4" />;
      default:
        return <div className="h-4 w-4" />;
    }
  };

  const getStepText = (stepType: string) => {
    switch (stepType) {
      case 'send_whatsapp':
        return 'שלח הודעת וואטסאפ';
      case 'wait':
        return 'המתן זמן מוגדר';
      case 'update_lead_status':
        return 'עדכן סטטוס ליד';
      case 'update_case_status':
        return 'עדכן סטטוס תיק';
      case 'create_case':
        return 'צור תיק חדש';
      default:
        return stepType;
    }
  };

  const leadStatuses = LEAD_STATUSES;

  const caseStatuses = [
    'בקליטה',
    'פתוח',
    'סגור'
  ];

  const availableStatuses = triggerType === 'lead_status_change' ? leadStatuses : caseStatuses;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            {editingWorkflow ? 'עריכת זרימת עבודה' : 'יצירת זרימת עבודה חדשה'}
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Step 1: Basic Info */}
          {currentStep === 1 && (
            <Card>
              <CardHeader>
                <CardTitle>שלב 1: פרטים בסיסיים</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="workflow-name">שם הזרימה *</Label>
                  <Input
                    id="workflow-name"
                    value={workflowName}
                    onChange={(e) => setWorkflowName(e.target.value)}
                    placeholder="הכנס שם לזרימת העבודה"
                    className={!workflowName.trim() ? 'border-red-300 focus:border-red-500' : ''}
                    required
                  />
                  {!workflowName.trim() && (
                    <p className="text-sm text-red-600 mt-1">שדה חובה</p>
                  )}
                </div>

                <div>
                  <Label htmlFor="workflow-description">תיאור (אופציונלי)</Label>
                  <Textarea
                    id="workflow-description"
                    value={workflowDescription}
                    onChange={(e) => setWorkflowDescription(e.target.value)}
                    placeholder="תאר את מטרת זרימת העבודה"
                    rows={3}
                  />
                </div>

                <div className="flex justify-end">
                  <Button 
                    onClick={() => setCurrentStep(2)}
                    disabled={!workflowName.trim()}
                  >
                    המשך לטריגר
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Step 2: Trigger Configuration */}
          {currentStep === 2 && (
            <Card>
              <CardHeader>
                <CardTitle>שלב 2: הגדרת טריגר</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label>סוג הטריגר</Label>
                  <Select value={triggerType} onValueChange={(value: any) => setTriggerType(value)}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="lead_status_change">שינוי סטטוס ליד</SelectItem>
                      <SelectItem value="case_status_change">שינוי סטטוס תיק</SelectItem>
                      <SelectItem value="manual">טריגר ידני</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* Status Change Configuration */}
                {(triggerType === 'lead_status_change' || triggerType === 'case_status_change') && (
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label>מסטטוס</Label>
                      <Select value={fromStatus} onValueChange={setFromStatus}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="any">כל סטטוס</SelectItem>
                          {availableStatuses.map(status => (
                            <SelectItem key={status} value={status}>{status}</SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <div>
                      <Label>לסטטוס *</Label>
                      <Select value={toStatus} onValueChange={setToStatus}>
                        <SelectTrigger className={!toStatus ? 'border-red-300 focus:border-red-500' : ''}>
                          <SelectValue placeholder="בחר סטטוס יעד" />
                        </SelectTrigger>
                        <SelectContent>
                          {availableStatuses.map(status => (
                            <SelectItem key={status} value={status}>{status}</SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      {!toStatus && (
                        <p className="text-sm text-red-600 mt-1">שדה חובה</p>
                      )}
                    </div>
                  </div>
                )}

                {/* Manual Trigger Configuration */}
                {triggerType === 'manual' && (
                  <div className="space-y-4">
                    <div>
                      <Label>סטטוסים לטיפול *</Label>
                      <p className="text-sm text-muted-foreground mb-2">
                        בחר את הסטטוסים של הלידים שיכללו בטריגר הידני
                      </p>
                      <div className="grid grid-cols-2 gap-3">
                        {LEAD_STATUSES.map(status => (
                          <div
                            key={status}
                            className={`relative flex items-center justify-between p-3 border-2 rounded-lg cursor-pointer transition-all duration-200 hover:shadow-md ${
                              targetStatuses.includes(status)
                                ? 'border-primary bg-primary/5 shadow-sm'
                                : 'border-gray-200 hover:border-gray-300 bg-background'
                            }`}
                            onClick={() => {
                              if (targetStatuses.includes(status)) {
                                setTargetStatuses(prev => prev.filter(s => s !== status));
                              } else {
                                setTargetStatuses(prev => [...prev, status]);
                              }
                            }}
                          >
                            <div className="flex items-center gap-3">
                              <div className={`w-4 h-4 rounded border-2 flex items-center justify-center transition-colors ${
                                targetStatuses.includes(status)
                                  ? 'border-primary bg-primary'
                                  : 'border-gray-300'
                              }`}>
                                {targetStatuses.includes(status) && (
                                  <svg className="w-2.5 h-2.5 text-white" fill="currentColor" viewBox="0 0 20 20">
                                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                                  </svg>
                                )}
                              </div>
                              <span className={`text-sm font-medium ${
                                targetStatuses.includes(status) ? 'text-primary' : 'text-foreground'
                              }`}>
                                {status}
                              </span>
                            </div>
                            {targetStatuses.includes(status) && (
                              <div className="absolute -top-1 -right-1 w-3 h-3 bg-primary rounded-full border-2 border-background"></div>
                            )}
                          </div>
                        ))}
                      </div>
                      {targetStatuses.length === 0 && (
                        <p className="text-sm text-red-600 mt-1">יש לבחור לפחות סטטוס אחד</p>
                      )}
                    </div>

                    <div>
                      <Label htmlFor="manual-description">תיאור הקמפיין (אופציונלי)</Label>
                      <Textarea
                        id="manual-description"
                        value={manualDescription}
                        onChange={(e) => setManualDescription(e.target.value)}
                        placeholder="תאר את מטרת הקמפיין הידני"
                        rows={2}
                      />
                    </div>
                  </div>
                )}

                <div className="flex justify-between">
                  <Button variant="outline" onClick={() => setCurrentStep(1)}>
                    חזור
                  </Button>
                  <Button
                    onClick={() => setCurrentStep(3)}
                    disabled={
                      (triggerType === 'lead_status_change' || triggerType === 'case_status_change') ? !toStatus :
                      triggerType === 'manual' ? targetStatuses.length === 0 : false
                    }
                  >
                    המשך לפעולות
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Step 3: Actions Configuration */}
          {currentStep === 3 && (
            <Card>
              <CardHeader>
                <CardTitle>שלב 3: הגדרת פעולות</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Add Step Buttons */}
                <div>
                  <Label>הוסף פעולה</Label>
                  <div className="flex flex-wrap gap-2 mt-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => addStep('send_whatsapp')}
                    >
                      <MessageSquare className="h-4 w-4 mr-2" />
                      שלח וואטסאפ
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => addStep('wait')}
                    >
                      <Clock className="h-4 w-4 mr-2" />
                      המתן
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => addStep('update_lead_status')}
                    >
                      <ArrowRight className="h-4 w-4 mr-2" />
                      עדכן ליד
                    </Button>
                    {triggerType === 'case_status_change' && (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => addStep('update_case_status')}
                      >
                        <ArrowRight className="h-4 w-4 mr-2" />
                        עדכן תיק
                      </Button>
                    )}
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => addStep('create_case')}
                    >
                      <FileText className="h-4 w-4 mr-2" />
                      צור תיק
                    </Button>
                  </div>
                </div>

                <Separator />

                {/* Steps List */}
                <div className="space-y-3">
                  {steps.map((step, index) => (
                    <Card key={index} className="border-l-4 border-l-primary">
                      <CardHeader className="pb-2">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-2">
                            <Badge variant="outline">{index + 1}</Badge>
                            {getStepIcon(step.step_type)}
                            <span className="font-medium">{getStepText(step.step_type)}</span>
                          </div>
                          <div className="flex items-center gap-1">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => moveStep(index, 'up')}
                              disabled={index === 0}
                              className="h-8 w-8 p-0"
                            >
                              ↑
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => moveStep(index, 'down')}
                              disabled={index === steps.length - 1}
                              className="h-8 w-8 p-0"
                            >
                              ↓
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => removeStep(index)}
                              className="h-8 w-8 p-0 text-destructive hover:text-destructive"
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>
                      </CardHeader>
                      <CardContent className="pt-0">
                        <StepConfigForm
                          stepType={step.step_type}
                          config={step.step_config}
                          onChange={(config) => updateStep(index, config)}
                          triggerType={triggerType}
                        />
                      </CardContent>
                    </Card>
                  ))}
                </div>

                {steps.length === 0 && (
                  <div className="text-center py-8 text-muted-foreground">
                    הוסף לפחות פעולה אחת לזרימת העבודה
                  </div>
                )}

                <div className="flex justify-between">
                  <Button variant="outline" onClick={() => setCurrentStep(2)}>
                    חזור
                  </Button>
                  <div className="flex gap-2">
                    <Button variant="outline" onClick={onClose}>
                      <X className="h-4 w-4 mr-2" />
                      ביטול
                    </Button>
                    <Button 
                      onClick={handleSave}
                      disabled={steps.length === 0 || isSaving}
                    >
                      <Save className="h-4 w-4 mr-2" />
                      {isSaving ? 'שומר...' : 'שמור זרימה'}
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
};
