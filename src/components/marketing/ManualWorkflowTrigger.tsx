import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, CardH<PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useWorkflows } from '@/hooks/useWorkflows';
import { useLeads } from '@/hooks/useLeads';
import { supabase } from '@/integrations/supabase/client';
import { useCompany } from '@/contexts/CompanyContext';
import { toast } from 'sonner';
import { 
  Play, 
  Users, 
  Filter,
  Loader2,
  Workflow,
  Target
} from 'lucide-react';
import { LEAD_STATUSES } from '@/constants/leadStatuses';

export const ManualWorkflowTrigger = () => {
  const { workflows } = useWorkflows();
  const { leads } = useLeads();
  const { currentCompany } = useCompany();
  const [selectedWorkflow, setSelectedWorkflow] = useState<string>('');
  const [selectedStatuses, setSelectedStatuses] = useState<string[]>([]);
  const [isExecuting, setIsExecuting] = useState(false);

  // Filter workflows to only show manual trigger types
  const manualWorkflows = workflows.filter(workflow => 
    workflow.trigger_type === 'manual' && workflow.is_active
  );

  // Get leads matching selected statuses
  const matchingLeads = leads.filter(lead => 
    selectedStatuses.includes(lead.status)
  );

  const handleStatusToggle = (status: string, checked: boolean) => {
    if (checked) {
      setSelectedStatuses(prev => [...prev, status]);
    } else {
      setSelectedStatuses(prev => prev.filter(s => s !== status));
    }
  };

  const handleSelectAllStatuses = (checked: boolean) => {
    if (checked) {
      setSelectedStatuses([...LEAD_STATUSES]);
    } else {
      setSelectedStatuses([]);
    }
  };

  const executeManualWorkflow = async () => {
    if (!selectedWorkflow || selectedStatuses.length === 0) {
      toast.error('אנא בחר זרימה וסטטוסים');
      return;
    }

    if (matchingLeads.length === 0) {
      toast.error('לא נמצאו לידים מתאימים לסטטוסים שנבחרו');
      return;
    }

    setIsExecuting(true);
    try {
      const { data, error } = await supabase.functions.invoke('workflow-executor', {
        body: {
          action: 'trigger_manual',
          manualTriggerData: {
            workflow_id: selectedWorkflow,
            target_statuses: selectedStatuses,
            company_id: currentCompany?.id
          }
        }
      });

      if (error) throw error;

      const result = data;
      if (result.success) {
        toast.success(`זרימה הופעלה בהצלחה עבור ${result.leads_processed} לידים`);
        
        if (result.errors && result.errors.length > 0) {
          console.warn('Some leads had errors:', result.errors);
          toast.warning(`${result.errors.length} לידים נכשלו - בדוק את הלוגים`);
        }
      } else {
        throw new Error(result.error || 'שגיאה לא ידועה');
      }
    } catch (error) {
      console.error('Error executing manual workflow:', error);
      toast.error('שגיאה בהפעלת הזרימה');
    } finally {
      setIsExecuting(false);
    }
  };

  const selectedWorkflowData = manualWorkflows.find(w => w.id === selectedWorkflow);

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Target className="h-5 w-5" />
            <CardTitle>הפעלה ידנית של זרימות</CardTitle>
          </div>
          <div className="flex items-center gap-2">
            <Badge variant="outline" className="flex items-center gap-1">
              <Workflow className="w-3 h-3" />
              {manualWorkflows.length} זרימות זמינות
            </Badge>
            <Badge variant="outline" className="flex items-center gap-1">
              <Users className="w-3 h-3" />
              {matchingLeads.length} לידים מתאימים
            </Badge>
          </div>
        </div>
        <p className="text-sm text-muted-foreground">
          בחר זרימה וסטטוסים להפעלה ידנית של קמפיין
        </p>
      </CardHeader>
      
      <CardContent className="space-y-6">
        {/* Workflow Selection */}
        <div>
          <label className="text-sm font-medium mb-2 block">בחר זרימה</label>
          <Select value={selectedWorkflow} onValueChange={setSelectedWorkflow}>
            <SelectTrigger>
              <SelectValue placeholder="בחר זרימה להפעלה" />
            </SelectTrigger>
            <SelectContent>
              {manualWorkflows.length === 0 ? (
                <div className="p-4 text-center text-muted-foreground">
                  אין זרימות ידניות זמינות
                </div>
              ) : (
                manualWorkflows.map((workflow) => (
                  <SelectItem key={workflow.id} value={workflow.id}>
                    <div className="flex items-center gap-2">
                      <span>{workflow.name}</span>
                      <Badge variant="secondary" className="text-xs">
                        {workflow.workflow_steps?.length || 0} שלבים
                      </Badge>
                    </div>
                  </SelectItem>
                ))
              )}
            </SelectContent>
          </Select>
          
          {selectedWorkflowData && (
            <div className="mt-2 p-3 bg-muted/30 rounded-lg">
              <p className="text-sm font-medium">{selectedWorkflowData.name}</p>
              {selectedWorkflowData.description && (
                <p className="text-xs text-muted-foreground mt-1">
                  {selectedWorkflowData.description}
                </p>
              )}
              <div className="flex items-center gap-2 mt-2">
                <Badge variant="outline" className="text-xs">
                  {selectedWorkflowData.workflow_steps?.length || 0} שלבים
                </Badge>
                <Badge variant={selectedWorkflowData.is_active ? "default" : "secondary"} className="text-xs">
                  {selectedWorkflowData.is_active ? 'פעיל' : 'מושבת'}
                </Badge>
              </div>
            </div>
          )}
        </div>

        {/* Status Selection */}
        <div>
          <div className="flex items-center justify-between mb-3">
            <label className="text-sm font-medium">בחר סטטוסים</label>
            <div className="flex items-center gap-2">
              <Checkbox
                checked={selectedStatuses.length === LEAD_STATUSES.length}
                onCheckedChange={handleSelectAllStatuses}
              />
              <span className="text-sm">בחר הכל</span>
            </div>
          </div>
          
          <div className="grid grid-cols-2 gap-3">
            {LEAD_STATUSES.map(status => {
              const statusLeads = leads.filter(lead => lead.status === status);
              return (
                <div
                  key={status}
                  className={`flex items-center justify-between p-3 border rounded-lg transition-colors ${
                    selectedStatuses.includes(status) 
                      ? 'bg-primary/5 border-primary/20' 
                      : 'hover:bg-muted/50'
                  }`}
                >
                  <div className="flex items-center gap-2">
                    <Checkbox
                      checked={selectedStatuses.includes(status)}
                      onCheckedChange={(checked) => handleStatusToggle(status, checked as boolean)}
                    />
                    <span className="text-sm font-medium">{status}</span>
                  </div>
                  <Badge variant="outline" className="text-xs">
                    {statusLeads.length}
                  </Badge>
                </div>
              );
            })}
          </div>
        </div>

        {/* Matching Leads Preview */}
        {selectedStatuses.length > 0 && (
          <div>
            <label className="text-sm font-medium mb-2 block">
              לידים שיכללו בהפעלה ({matchingLeads.length})
            </label>
            <ScrollArea className="h-[200px] border rounded-lg">
              <div className="p-3 space-y-2">
                {matchingLeads.length === 0 ? (
                  <div className="text-center py-8 text-muted-foreground">
                    <Users className="w-12 h-12 mx-auto mb-4 opacity-50" />
                    <p>אין לידים מתאימים לסטטוסים שנבחרו</p>
                  </div>
                ) : (
                  matchingLeads.slice(0, 50).map((lead) => (
                    <div
                      key={lead.id}
                      className="flex items-center justify-between p-2 bg-background border rounded text-sm"
                    >
                      <div>
                        <span className="font-medium">{lead.full_name}</span>
                        <span className="text-muted-foreground ml-2">{lead.phone}</span>
                      </div>
                      <Badge variant="outline" className="text-xs">
                        {lead.status}
                      </Badge>
                    </div>
                  ))
                )}
                {matchingLeads.length > 50 && (
                  <div className="text-center py-2 text-muted-foreground text-sm">
                    ועוד {matchingLeads.length - 50} לידים נוספים...
                  </div>
                )}
              </div>
            </ScrollArea>
          </div>
        )}

        {/* Execute Button */}
        <div className="flex justify-end">
          <Button
            onClick={executeManualWorkflow}
            disabled={!selectedWorkflow || selectedStatuses.length === 0 || matchingLeads.length === 0 || isExecuting}
            className="flex items-center gap-2"
            size="lg"
          >
            {isExecuting ? (
              <Loader2 className="w-4 h-4 animate-spin" />
            ) : (
              <Play className="w-4 h-4" />
            )}
            {isExecuting ? 'מפעיל...' : `הפעל זרימה (${matchingLeads.length} לידים)`}
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};
